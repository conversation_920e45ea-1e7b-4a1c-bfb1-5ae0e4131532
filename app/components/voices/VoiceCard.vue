<template>
  <UCard
    :ui="{
      body: '!p-0'
    }"
    v-bind="$attrs"
    class="group transition-all duration-300"
  >
    <div
      class="flex flex-row items-center"
      :class="{ 'px-3 py-1': mini, 'p-3': !mini }"
    >
      <div>
        <div
          class="h-10 w-10 bg-neutral-100 dark:bg-neutral-800 rounded-full relative justify-between items-center overflow-hidden"
        >
          <!-- Custom SVG Avatar for Gemini voices -->
          <img
            v-if="voice.type === 'gemini_voice' && voice.icon.startsWith('data:image/svg+xml')"
            :src="voice.icon"
            :alt="voice.speaker_name"
            class="w-full h-full object-cover"
          />
          <!-- Regular icon for other voices -->
          <UIcon
            v-else
            :name="voice.icon"
            class="w-7 h-7 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
          />
          <div
            v-if="isPlaying"
            class="absolute rounded-full animate-ping inset-0 w-full h-full bg-primary-500/30 z-20"
          />
          <div
            class="group-hover:flex cursor-pointer z-30 h-10 w-10 rounded-full bg-black/20 backdrop-blur-[2px] border border-white/20 shadow-lg absolute inset-0"
            :class="{
              flex: isPlaying,
              hidden: !isPlaying
            }"
            @click.stop="$emit('play-preview', voice)"
          >
            <UIcon
              v-if="isPlaying"
              name="solar:pause-bold"
              class="w-4 h-4 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            />
            <UIcon
              v-else
              name="solar:play-bold"
              class="w-4 h-4 text-white absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2"
            />
          </div>
        </div>
      </div>
      <div class="flex-1 ml-2 flex flex-col gap-1">
        <div class="flex flex-row items-center gap-2">
          <div class="text-xs">
            {{ props.voice.speaker_name }}
          </div>
          <div class="flex flex-row items-center gap-1">
            <UBadge
              size="xs"
              color="neutral"
              variant="subtle"
            >
              {{ $t(voice.age) }}
            </UBadge>
            <UBadge
              size="xs"
              color="neutral"
              variant="subtle"
            >
              {{ $t(voice.gender) }}
            </UBadge>
          </div>
        </div>
        <div
          class="text-xs text-gray-500 dark:text-gray-400 line-clamp-2 group-hover:line-clamp-none"
        >
          {{ props.voice.description }}
        </div>
      </div>
      <div class="ml-2">
        <UButton
          :icon="
            updatings?.[props.voice.id]
              ? 'eos-icons:loading'
              : props.voice.is_favorite
                ? 'material-symbols:favorite'
                : 'material-symbols:favorite-outline'
          "
          color="primary"
          variant="ghost"
          :class="{
            'opacity-0 group-hover:opacity-100 transition-opacity duration-100':
              !mini && !props.voice.is_favorite
          }"
          :disabled="updatings?.[props.voice.id]"
          @click.stop="toggleFavorite(props.voice.id, !props.voice.is_favorite)"
        />
      </div>
    </div>
  </UCard>
</template>

<script setup lang="ts">
import type { SpeechVoice } from '~/composables/useSpeechVoices'

interface VoiceCardProps {
  voice: SpeechVoice
  isPlaying?: boolean
  mini?: boolean
}

const props = defineProps<VoiceCardProps>()

const { toggleFavorite, updatings } = useSpeechVoices()
</script>
