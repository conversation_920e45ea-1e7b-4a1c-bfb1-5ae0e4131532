<script setup lang="ts">
interface ComparisonExample {
  id: string
  beforeImage: string
  afterImage: string
  beforeLabel: string
  afterLabel: string
  title: string
  description?: string
}

interface Props {
  examples?: ComparisonExample[]
  height?: string
  autoplay?: boolean
  interval?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  autoplay: true,
  interval: 5000,
  examples: () => [
    {
      id: '1',
      beforeImage: 'https://static.aiquickdraw.com/images/ghibli/0-1.webp?v=1',
      afterImage: 'https://static.aiquickdraw.com/images/ghibli/0-2.webp?v=1',
      beforeLabel: 'Original',
      afterLabel: 'Ghibli Style',
      title: 'Anime Style Transfer',
      description: 'Transform images into anime style with AI'
    },
    {
      id: '2',
      beforeImage: 'https://static.aiquickdraw.com/images/ghibli/1-1.webp?v=1',
      afterImage: 'https://static.aiquickdraw.com/images/ghibli/1-2.webp?v=1',
      beforeLabel: 'Original',
      afterLabel: 'Ghibli Style',
      title: 'Anime Style Transfer',
      description: 'Transform images into anime style with AI'
    },
    {
      id: '3',
      beforeImage: 'https://static.aiquickdraw.com/images/ghibli/2-1.webp?v=1',
      afterImage: 'https://static.aiquickdraw.com/images/ghibli/2-2.webp?v=1',
      beforeLabel: 'Original',
      afterLabel: 'Ghibli Style',
      title: 'Anime Style Transfer',
      description: 'Transform images into anime style with AI'
    },
    {
      id: '4',
      beforeImage:
        'https://lh3.googleusercontent.com/uMhb3MGmuJ72mWzjLmADnvbX9S-Bl5a6-nLtE3RN3YAGLJLnv3zRwiXbiZbSTMGfLQEkmZxJcyYNoGVTyZxjMNMH9hbFNGWlfwBdqa_ugLjEFSYITg=w1024-rw',
      afterImage:
        'https://fa1030eacc97e2f2ca187ef328dddf17.r2.cloudflarestorage.com/geminigen-dev-upload-bucket/20/generated_result/image/9e7b90ba-5fa8-11f0-afcd-7a3c1801fb5d/gen/9e7b90ba-5fa8-11f0-afcd-7a3c1801fb5d_1.png?response-content-type=application%2Foctet-stream&X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=9e4aaa0a83527e9fde114e51284a68ed%2F20250713%2Fus-east-1%2Fs3%2Faws4_request&X-Amz-Date=20250713T051734Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=92ebb4e344e8bcd61ddbd08d11e79f72ddbcc57565a03dfe4b41a192ae55e209',
      beforeLabel: 'Low Quality',
      afterLabel: 'HD Quality',
      title: 'Image Upscaling',
      description: 'Enhance image resolution and quality with AI'
    }
  ]
})

const currentIndex = ref(0)
const isAutoplayActive = ref(props.autoplay)
let autoplayTimer: NodeJS.Timeout | null = null

// Tutorial state
const showTutorial = ref(false)
const isFirstVisit = ref(true)
const tutorialSliderPosition = ref(50)
let tutorialTimer: NodeJS.Timeout | null = null
let sliderDemoTimer: NodeJS.Timeout | null = null

const startAutoplay = () => {
  if (!isAutoplayActive.value || props.examples.length <= 1) return

  autoplayTimer = setInterval(() => {
    currentIndex.value = (currentIndex.value + 1) % props.examples.length
  }, props.interval)
}

const stopAutoplay = () => {
  if (autoplayTimer) {
    clearInterval(autoplayTimer)
    autoplayTimer = null
  }
}

const toggleAutoplay = () => {
  isAutoplayActive.value = !isAutoplayActive.value
  if (isAutoplayActive.value) {
    startAutoplay()
  } else {
    stopAutoplay()
  }
}

const goToSlide = (index: number) => {
  currentIndex.value = index
  stopAutoplay()
  if (isAutoplayActive.value) {
    startAutoplay()
  }
}

const nextSlide = () => {
  currentIndex.value = (currentIndex.value + 1) % props.examples.length
  stopAutoplay()
  if (isAutoplayActive.value) {
    startAutoplay()
  }
}

const prevSlide = () => {
  currentIndex.value
    = currentIndex.value === 0
      ? props.examples.length - 1
      : currentIndex.value - 1
  stopAutoplay()
  if (isAutoplayActive.value) {
    startAutoplay()
  }
}

onMounted(() => {
  // Check if user has seen tutorial before
  if (typeof window !== 'undefined') {
    const hasSeenTutorial = localStorage.getItem('image-comparison-tutorial-seen')
    if (hasSeenTutorial) {
      isFirstVisit.value = false
    }
  }

  if (isAutoplayActive.value) {
    startAutoplay()
  }

  // Start tutorial for first-time users
  if (isFirstVisit.value) {
    startTutorial()
  }
})

onUnmounted(() => {
  stopAutoplay()

  // Clean up tutorial timers
  if (tutorialTimer) {
    clearTimeout(tutorialTimer)
  }
  if (sliderDemoTimer) {
    clearTimeout(sliderDemoTimer)
  }
})

// Pause autoplay on hover
const onMouseEnter = () => {
  if (isAutoplayActive.value) {
    stopAutoplay()
  }
}

const onMouseLeave = () => {
  if (isAutoplayActive.value) {
    startAutoplay()
  }
}

// Tutorial functions
const startTutorial = () => {
  if (!isFirstVisit.value) return

  // Show tutorial popup after 1 second
  tutorialTimer = setTimeout(() => {
    showTutorial.value = true
    startSliderDemo()
  }, 1000)
}

const startSliderDemo = () => {
  let direction = 1
  const animateSlider = () => {
    if (!showTutorial.value) return

    if (direction === 1) {
      tutorialSliderPosition.value += 2
      if (tutorialSliderPosition.value >= 80) {
        direction = -1
      }
    } else {
      tutorialSliderPosition.value -= 2
      if (tutorialSliderPosition.value <= 20) {
        direction = 1
      }
    }

    sliderDemoTimer = setTimeout(animateSlider, 50)
  }

  animateSlider()
}

const closeTutorial = () => {
  showTutorial.value = false
  isFirstVisit.value = false

  if (tutorialTimer) {
    clearTimeout(tutorialTimer)
    tutorialTimer = null
  }

  if (sliderDemoTimer) {
    clearTimeout(sliderDemoTimer)
    sliderDemoTimer = null
  }

  // Store in localStorage to remember user has seen tutorial
  if (typeof window !== 'undefined') {
    localStorage.setItem('image-comparison-tutorial-seen', 'true')
  }
}
</script>

<template>
  <div
    class="relative w-full"
    @mouseenter="onMouseEnter"
    @mouseleave="onMouseLeave"
  >
    <!-- Main Comparison Slider -->
    <div class="relative">
      <BaseImageComparisonSlider
        v-if="props.examples[currentIndex]"
        :before-image="props.examples[currentIndex].beforeImage"
        :after-image="props.examples[currentIndex].afterImage"
        :before-label="props.examples[currentIndex].beforeLabel"
        :after-label="props.examples[currentIndex].afterLabel"
        :height="props.height"
        :initial-position="showTutorial ? tutorialSliderPosition : 50"
      />

      <!-- Tutorial Overlay -->
      <div
        v-if="showTutorial"
        class="absolute inset-0 bg-black/20 backdrop-blur-sm z-40 flex items-center justify-center"
        @click="closeTutorial"
      >
        <div
          class="bg-white dark:bg-gray-800 rounded-lg p-6 mx-4 max-w-sm shadow-xl border border-gray-200 dark:border-gray-700"
          @click.stop
        >
          <div class="flex items-center mb-4">
            <UIcon
              name="i-lucide-mouse-pointer-click"
              class="w-6 h-6 text-primary-500 mr-3"
            />
            <h3 class="text-lg font-semibold text-gray-900 dark:text-white">
              {{ $t('Try the Comparison!') }}
            </h3>
          </div>
          <p class="text-sm text-gray-600 dark:text-gray-400 mb-4">
            {{ $t('Drag the slider left and right to compare the before and after images. You can also click anywhere on the image to move the slider.') }}
          </p>
          <div class="flex justify-end space-x-2">
            <UButton
              variant="ghost"
              size="sm"
              @click="closeTutorial"
            >
              {{ $t('Got it!') }}
            </UButton>
          </div>
        </div>
      </div>

      <!-- Autoplay Control - Bottom Right -->
      <div
        v-if="props.examples.length > 1"
        class="absolute bottom-4 right-4 z-30"
      >
        <UButton
          class="bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 backdrop-blur-sm"
          @click="toggleAutoplay"
        >
          <UIcon
            :name="isAutoplayActive ? 'i-lucide-pause' : 'i-lucide-play'"
            class="w-4 h-4"
          />
        </UButton>
      </div>

      <!-- Navigation Arrows -->
      <UButton
        v-if="props.examples.length > 1"
        class="absolute left-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-20"
        @click="prevSlide"
      >
        <UIcon
          name="i-lucide-chevron-left"
          class="w-5 h-5"
        />
      </UButton>

      <UButton
        v-if="props.examples.length > 1"
        class="absolute right-4 top-1/2 transform -translate-y-1/2 bg-black/50 hover:bg-black/70 text-white p-2 rounded-full transition-all duration-200 z-20"
        @click="nextSlide"
      >
        <UIcon
          name="i-lucide-chevron-right"
          class="w-5 h-5"
        />
      </UButton>
    </div>

    <!-- Example Info -->
    <div
      v-if="props.examples[currentIndex]"
      class="mt-4 text-center"
    >
      <h3 class="text-lg font-semibold text-gray-900 dark:text-white mb-1">
        {{ props.examples[currentIndex].title }}
      </h3>
      <p
        v-if="props.examples[currentIndex].description"
        class="text-sm text-gray-600 dark:text-gray-400"
      >
        {{ props.examples[currentIndex].description }}
      </p>
    </div>

    <!-- Dots Indicator -->
    <div
      v-if="props.examples.length > 1"
      class="flex justify-center mt-4 space-x-2"
    >
      <UButton
        v-for="(example, index) in props.examples"
        :key="example.id"
        class="w-2 h-2 rounded-full transition-all duration-200"
        :class="
          index === currentIndex
            ? 'bg-primary-500'
            : 'bg-gray-300 dark:bg-gray-600 hover:bg-gray-400 dark:hover:bg-gray-500'
        "
        @click="goToSlide(index)"
      />
    </div>
  </div>
</template>
